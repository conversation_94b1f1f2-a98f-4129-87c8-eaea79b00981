package com.holidu.referral.infrastructure.repository.referrer

import com.holidu.referral.referrer.Referrer
import org.jetbrains.annotations.TestOnly
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class ReferrerRepository(
    private val referrerSpringJpaRepository: ReferrerSpringJpaRepository
) {
    fun saveAndIndex(referrer: Referrer): Referrer =
        referrerSpringJpaRepository.save(referrer)

    fun findAll(): List<Referrer> =
        referrerSpringJpaRepository.findAll()

    fun findByLegalEntityId(legalEntityId: Long): Referrer? =
        referrerSpringJpaRepository.findByLegalEntityId(legalEntityId)

    fun findByReferralCode(referralCode: String): Referrer? =
        referrerSpringJpaRepository.findByReferralCode(referralCode)

    fun getReferenceById(id: Long): Referrer =
        referrerSpringJpaRepository.getReferenceById(id)

    fun findByIdOrNull(id: Long): Referrer? =
        referrerSpringJpaRepository.findByIdOrNull(id)

    @TestOnly
    fun delete(referrer: Referrer): Unit =
        referrerSpringJpaRepository.delete(referrer)
}