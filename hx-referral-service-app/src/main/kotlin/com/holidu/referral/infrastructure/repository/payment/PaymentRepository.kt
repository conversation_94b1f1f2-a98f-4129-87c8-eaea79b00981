package com.holidu.referral.infrastructure.repository.payment

import com.holidu.referral.infrastructure.datalake.DatalakeIndexer
import com.holidu.referral.infrastructure.datalake.Indexer
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.Payment
import com.holidu.referral.payment.PaymentState
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class PaymentRepository(
    private val paymentSpringJpaRepository: PaymentSpringJpaRepository,
    private val indexer: Indexer
) {
    fun saveAndIndex(payment: Payment): Payment =
        paymentSpringJpaRepository.save(payment)
            .also {
                indexer.index(payment)
            }

    fun findAll(): List<Payment> =
        paymentSpringJpaRepository.findAll()

    fun findByIdOrNull(id: Long): Payment? =
        paymentSpringJpaRepository.findByIdOrNull(id)

    fun findOne(id: Long): Payment? =
        paymentSpringJpaRepository.findOne(id)

    fun findByReferrerIdAndLinkedEntityTypeIn(
        referrerId: Long,
        linkedEntityTypes: Set<LinkedEntityType>
    ): List<Payment> =
        paymentSpringJpaRepository.findByReferrerIdAndLinkedEntityTypeIn(
            referrerId = referrerId,
            linkedEntityTypes = linkedEntityTypes
        )

    fun findByUnitTypeId(unitTypeId: Long): Payment? =
        paymentSpringJpaRepository.findByUnitTypeId(unitTypeId)

    fun findByBonusId(bonusId: Long): Payment? =
        paymentSpringJpaRepository.findByBonusId(bonusId)

    fun findByState(state: PaymentState): List<Payment> =
        paymentSpringJpaRepository.findByState(state)

    fun findByReferrerIdAndLinkedEntityTypeAndState(
        referrerId: Long,
        linkedEntityType: LinkedEntityType,
        state: PaymentState
    ): List<Payment> =
        paymentSpringJpaRepository.findByReferrerIdAndLinkedEntityTypeAndState(
            referrerId = referrerId,
            linkedEntityType = linkedEntityType,
            state = state
        )

    fun findByReferrerId(referrerId: Long): List<Payment> =
        paymentSpringJpaRepository.findByReferrerId(referrerId)

    fun getTotalRewardForReferrerInCurrentMonth(referrerId: Long): Int =
        paymentSpringJpaRepository.getTotalRewardForReferrerInCurrentMonth(referrerId)
}