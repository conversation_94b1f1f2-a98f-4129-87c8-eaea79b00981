package com.holidu.referral.infrastructure.repository.referee

import com.holidu.referral.referee.Referee
import org.springframework.stereotype.Component

@Component
class RefereeRepository(
    private val refereeSpringJpaRepository: RefereeSpringJpaRepository
) {
    fun saveAndIndex(referee: Referee): Referee =
        refereeSpringJpaRepository.save(referee)

    fun findAll(): List<Referee> =
        refereeSpringJpaRepository.findAll()

    fun findByLegalEntityId(legalEntityId: Long): Referee? =
        refereeSpringJpaRepository.findByLegalEntityId(legalEntityId)

    fun findByNameIsNull(): List<Referee> =
        refereeSpringJpaRepository.findByNameIsNull()

    fun findByReferrerId(referrerId: Long): List<Referee> =
        refereeSpringJpaRepository.findByReferrerId(referrerId)
}