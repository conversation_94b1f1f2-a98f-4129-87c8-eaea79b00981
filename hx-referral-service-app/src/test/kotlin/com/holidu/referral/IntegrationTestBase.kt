package com.holidu.referral

import com.bookiply.apartment.dto.UnitTypeDto
import com.bookiply.apartment.model.ApartmentTypeEnum
import com.bookiply.collaborator.dto.AgentParentListingRelationDto
import com.bookiply.collaborator.model.PmcAgentParentListingRelationType
import com.bookiply.infrastructure.events.activemq.ActiveMqBrokerRegistry
import com.bookiply.infrastructure.events.config.DynamicJmsListenersRegistration
import com.bookiply.infrastructure.events.publishers.MockCommandPublisher
import com.bookiply.infrastructure.events.publishers.MockEventPublisher
import com.bookiply.rest.dto.SearchResponse
import com.holidu.referral.bonus.Bonus
import com.holidu.referral.bonus.BonusRepository
import com.holidu.referral.bonus.BonusType
import com.holidu.referral.domain.property.CreatedProperty
import com.holidu.referral.domain.property.PublishedProperty
import com.holidu.referral.domain.property.ReferredPropertyRepository
import com.holidu.referral.infrastructure.external.marketing.ZapierClient
import com.holidu.referral.infrastructure.external.marketing.ZapierService
import com.holidu.referral.infrastructure.repository.payment.PaymentRepository
import com.holidu.referral.infrastructure.repository.referee.RefereeRepository
import com.holidu.referral.infrastructure.repository.referrer.ReferrerRepository
import com.holidu.referral.invoice.InvoiceClient
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.Payment
import com.holidu.referral.payment.PaymentState
import com.holidu.referral.pms.client.*
import com.holidu.referral.referee.Referee
import com.holidu.referral.referrer.LegalEntityClient
import com.holidu.referral.referrer.ReferralCode
import com.holidu.referral.referrer.Referrer
import com.holidu.referral.reward.rule.RewardRule
import com.holidu.referral.reward.rule.RewardRuleRepository
import io.kotest.matchers.be
import io.kotest.matchers.should
import org.junit.jupiter.api.BeforeEach
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.graphql.tester.AutoConfigureHttpGraphQlTester
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.graphql.test.tester.WebGraphQlTester
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.reactive.server.WebTestClient
import java.time.*
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit.SECONDS
import kotlin.random.Random
import kotlin.random.nextInt

@ActiveProfiles("test")
@MockitoBean(
    types = [
        // mocking external (Feign) clients
        OAuth2AuthorizedClientManager::class,
        UnitTypeClient::class,
        UnitTypeAgentClient::class,
        ParentListingGeneralInfoClient::class,
        RewardRuleMigrationClient::class,
        FocusRegionClient::class,
        InvoiceClient::class,
        ZapierClient::class,
        // mocking ActiveMq and all @EventListener(s)
        ActiveMqBrokerRegistry::class,
        DynamicJmsListenersRegistration::class,
    ]
)
@MockitoBean(name = "legalEntityServiceTokenClient", types = [LegalEntityClient::class])
@MockitoBean(name = "legalEntityUserTokenClient", types = [LegalEntityClient::class])
@MockitoBean(
    name = "zapierService",
    types = [ZapierService::class]
) // mocked to verify the method invocation, as downstream client invocation happens only for "Live" env
@AutoConfigureHttpGraphQlTester
@AutoConfigureWebTestClient
@SpringBootTest(
    classes = [
        MockPublishersTestConfig::class,
        DatalakeTestConfig::class
    ],
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
abstract class IntegrationTestBase {

    @Autowired
    private lateinit var dataSource: javax.sql.DataSource

    @Autowired
    protected lateinit var webGraphQlTester: WebGraphQlTester

    @Autowired
    protected lateinit var referrerRepository: ReferrerRepository

    @Autowired
    protected lateinit var refereeRepository: RefereeRepository

    @Autowired
    protected lateinit var referredPropertyRepository: ReferredPropertyRepository

    @Autowired
    protected lateinit var rewardRuleRepository: RewardRuleRepository

    @Autowired
    protected lateinit var paymentRepository: PaymentRepository

    @Autowired
    protected lateinit var bonusRepository: BonusRepository

    @Autowired
    protected lateinit var legalEntityUserTokenClient: LegalEntityClient

    @Autowired
    protected lateinit var legalEntityServiceTokenClient: LegalEntityClient

    @Autowired
    lateinit var invoiceClient: InvoiceClient

    @Autowired
    protected lateinit var zapierService: ZapierService

    @Autowired
    protected lateinit var webTestClient: WebTestClient

    @Autowired
    protected lateinit var unitTypeAgentClient: UnitTypeAgentClient

    @Autowired
    protected lateinit var parentListingGeneralInfoClient: ParentListingGeneralInfoClient

    @Autowired
    protected lateinit var unitTypeClient: UnitTypeClient

    @Autowired
    protected lateinit var commandPublisher: MockCommandPublisher

    @Autowired
    protected lateinit var eventPublisher: MockEventPublisher

    @Autowired
    protected lateinit var mockFirehoseWriter: MockFirehoseWriter

    @BeforeEach
    fun setUpBaseTest() {
        cleanUpDb()
        commandPublisher.cleanSentMessages()
        eventPublisher.cleanSentMessages()
        mockFirehoseWriter.clearSentRecords()
    }

    private fun cleanUpDb() {
        dataSource.connection.use { connection ->
            connection.createStatement().use { stmt ->
                stmt.execute("TRUNCATE TABLE bonus CASCADE")
                stmt.execute("TRUNCATE TABLE payment CASCADE")
                stmt.execute("TRUNCATE TABLE referred_property CASCADE")
                stmt.execute("TRUNCATE TABLE reward_rule CASCADE")
                stmt.execute("TRUNCATE TABLE referee CASCADE")
                stmt.execute("TRUNCATE TABLE referrer CASCADE")
            }
            connection.commit()
        }
    }

    protected fun createReferrer(
        legalEntityId: Long = randomPositiveLong(),
        referralCode: String = ReferralCode.new().value,
        termsAndConditionsAccepted: Boolean = true,
        marketingEmailsAccepted: Boolean = true
    ): Referrer {
        val referrer = Referrer(
            legalEntityId = legalEntityId,
            referralCode = referralCode,
            marketingEmailsAccepted = marketingEmailsAccepted,
            termsAndConditionsAccepted = termsAndConditionsAccepted
        )

        return referrerRepository.saveAndIndex(
            referrer
        )
    }

    protected fun randomReferrerId(): Long {
        val referrer = createReferrer()

        return referrer.id
    }

    protected fun createReferee(
        legalEntityId: Long = randomPositiveLong(),
        referrerId: Long,
        name: String = "Test Referee"
    ): Referee {
        val referee = Referee(
            legalEntityId = legalEntityId,
            referrerId = referrerId,
            name = name
        )

        return refereeRepository.saveAndIndex(referee)
    }


    protected fun publishedProperty(
        referrerId: Long,
        refereeLegalEntityId: Long = 1001,
        propertyType: ApartmentTypeEnum = ApartmentTypeEnum.VACATION_HOME,
        creationDate: OffsetDateTime = NOW.minusDays(2),
        publicationDate: OffsetDateTime = NOW,
        unitTypeId: Long = 101L,
        parentListingId: Long = 101L
    ) = referredPropertyRepository.savePublishedProperty(
        PublishedProperty(
            refereeLegalEntityId = refereeLegalEntityId,
            referrerId = referrerId,
            unitTypeId = unitTypeId,
            creationDate = creationDate,
            parentListingId = parentListingId,
            propertyType = propertyType.name,
            publicationDate = publicationDate
        )
    )


    protected fun createdProperty(
        referrerId: Long,
        refereeLegalEntityId: Long = 1001,
        unitTypeId: Long = randomPositiveLong(),
        parentListingId: Long = randomPositiveLong(),
        creationDate: OffsetDateTime = NOW.minusDays(3),
    ): CreatedProperty {
        return referredPropertyRepository.saveCreatedProperty(
            CreatedProperty(
                refereeLegalEntityId = refereeLegalEntityId,
                referrerId = referrerId,
                unitTypeId = unitTypeId,
                creationDate = creationDate,
                parentListingId = parentListingId,
            )
        )
    }

    protected fun createRewardRule(
        startDate: OffsetDateTime = OffsetDateTime.now(UTC).minusDays(1),
        endDate: OffsetDateTime = OffsetDateTime.now(UTC).plusDays(1),
        reward: Int = 75,
        focusRegionId: Long? = null,
        referrerId: Long? = null,
    ): RewardRule {
        val rewardRule = RewardRule(
            startDate = startDate,
            endDate = endDate,
            reward = reward,
            referrerId = referrerId,
            focusRegionId = focusRegionId,
        )

        return rewardRuleRepository.save(rewardRule)
    }

    protected fun createBonus(
        referrerId: Long,
        reward: Int = randomReward(),
        type: BonusType = BonusType.THREE_X_BONUS,
        numberOfPayments: Int,
        achievedAt: OffsetDateTime = OffsetDateTime.now(UTC).minusDays(Random.nextInt(1..15).toLong())
    ): Bonus {
        val bonus = Bonus(
            referrerId = referrerId,
            type = type,
            reward = reward,
            achievedAt = achievedAt,
            numberOfPayments = numberOfPayments
        )

        return bonusRepository.save(bonus)
    }

    protected fun Month.toOffsetDateTime(): OffsetDateTime = OffsetDateTime.of(
        LocalDate.of(2025, this, 1),
        LocalTime.of(0, 0),
        UTC
    )

    protected fun createPayment(
        referrerId: Long,
        referrerLegalEntityId: Long = 1000,
        unitTypeId: Long? = randomPositiveLong(),
        bonusId: Long? = null,
        refereeLegalEntityId: Long = 1000,
        type: LinkedEntityType = LinkedEntityType.REFERRED_PROPERTY,
        creationDate: OffsetDateTime = NOW,
        state: PaymentState = PaymentState.PENDING,
        reward: Int = randomReward(),
        deletedAt: Instant? = null
    ): Payment {
        val payment = Payment(
            referrerId = referrerId,
            referrerLegalEntityId = referrerLegalEntityId,
            refereeLegalEntityId = refereeLegalEntityId,
            creationDate = creationDate,
            reward = reward,
            unitTypeId = unitTypeId,
            bonusId = bonusId,
            linkedEntityType = type,
            state = state,
            deletedAt = deletedAt,
        )

        return paymentRepository.saveAndIndex(payment)
    }

    protected fun pmsPropertyExistsWithEditorRelationship(
        unitTypeId: Long = 101L,
        parentListingId: Long = 202L,
        refereeLegalEntityId: Long = 303L,
        propertyType: ApartmentTypeEnum = ApartmentTypeEnum.APARTMENT,
        creationDate: OffsetDateTime = NOW.minusYears(1),
        published: Boolean = false,
        firstPublicationDate: OffsetDateTime? = null
    ) {
        whenever(unitTypeClient.getUnitType(unitTypeId))
            .thenReturn(
                UnitTypeDto.builder()
                    .id(unitTypeId)
                    .parentListingId(parentListingId)
                    .createdAt(creationDate)
                    .pmcApartmentType(propertyType.toWrapper())
                    .published(published)
                    .build()
            )
        whenever(unitTypeAgentClient.findAgentParentListingRelation(any()))
            .thenReturn(
                SearchResponse.empty<AgentParentListingRelationDto>().apply {
                    results = listOf(
                        AgentParentListingRelationDto.builder()
                            .agentId(refereeLegalEntityId)
                            .parentListingId(parentListingId)
                            .relationType(PmcAgentParentListingRelationType.EDITOR)
                            .build()
                    )
                }
            )
        if (firstPublicationDate != null) {
            whenever(parentListingGeneralInfoClient.getGeneralInfo(parentListingId = parentListingId))
                .thenReturn(
                    ParentListingGeneralInfoResponseDto(
                        id = parentListingId,
                        unitTypes = listOf(
                            UnitTypeGeneralInfoResponseDto(
                                id = unitTypeId,
                                apartmentType = propertyType,
                                createdAt = creationDate,
                                unitTypePublishInfoDto = UnitTypePublishInfoDto(firstPublished = firstPublicationDate.toLocalDateTime())
                            )
                        )
                    )
                )
        }
    }


    companion object {
        /**
         * Random numbers that are easy to operate with (as they're multiples of 100).
         */
        private val ARBITRARY_REWARDS = setOf(100, 200, 300, 400, 500)

        val NOW = OffsetDateTime.now(UTC)

        private fun randomPositiveLong() = Random.nextLong(from = 1, until = Long.MAX_VALUE)

        private fun randomReward(): Int = ARBITRARY_REWARDS.random()
    }

    infix fun OffsetDateTime.shouldBeUpToSeconds(expected: OffsetDateTime?) {
        this.truncatedTo(SECONDS) should be(expected?.truncatedTo(SECONDS))
    }
}
