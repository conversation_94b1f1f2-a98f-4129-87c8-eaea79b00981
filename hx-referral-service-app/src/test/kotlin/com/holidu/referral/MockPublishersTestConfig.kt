package com.holidu.referral

import com.bookiply.infrastructure.events.publishers.MockCommandPublisher
import com.bookiply.infrastructure.events.publishers.MockEventPublisher
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean

@TestConfiguration
class MockPublishersTestConfig {
    @Bean
    fun eventPublisher(): MockEventPublisher = MockEventPublisher()

    @Bean
    fun commandPublisher(): MockCommandPublisher = MockCommandPublisher()
}